# Environment Variable Setting
################################################################################
dotenv_path: null


# Logging Setting
################################################################################
log_root_dir: logs/hotpotqa

# experiment_name: would be used to create log_dir = log_root_dir/experiment_name/
experiment_name: self_ask

# test_jsonl_filename: would be used to create test_jsonl_path = log_dir/test_jsonl_filename;
#   if set to null, the experiment_name would be used
test_jsonl_filename: null

# Number of rounds you want to test. min, max, avg. accuracy will be reported if multiple rounds.
test_rounds: 1


# Workflow Setting
################################################################################
workflow:
  module_path: pikerag.workflows.qa_self_ask
  class_name: QaSelfAskWorkflow


# Testing Suite Setting
################################################################################
test_loading:
  module: pikerag.utils.data_protocol_utils
  name: load_testing_suite
  args:
    filepath: data/hotpotqa/dev_500.jsonl


# Prompt Setting
################################################################################
self_ask_protocol:
  module_path: pikerag.prompts.self_ask
  protocol_name: self_ask_protocol

self_ask_intermediate_stop:
  module_path: pikerag.prompts.self_ask
  variable_name: IntermediateStop

followup_qa_protocol:
  module_path: pikerag.prompts.qa
  protocol_name: generation_qa_with_reference_protocol


# LLM Setting
################################################################################
llm_client:
  module_path: pikerag.llm_client
  # available class_name: AzureMetaLlamaClient, AzureOpenAIClient, HFMetaLlamaClient
  class_name: AzureOpenAIClient
  args: {}

  llm_config:
    model: gpt-4
    temperature: 0

  cache_config:
    # location_prefix: will be joined with log_dir to generate the full path;
    #   if set to null, the experiment_name would be used
    location_prefix: null
    auto_dump: True


# Retriever Setting
################################################################################
retriever:
  module_path: pikerag.knowledge_retrievers
  class_name: BaseQaRetriever
  args: {}


# Evaluator Setting
################################################################################
evaluator:
  metrics:
    - ExactMatch
    - F1
    - Precision
    - Recall
    - LLM
